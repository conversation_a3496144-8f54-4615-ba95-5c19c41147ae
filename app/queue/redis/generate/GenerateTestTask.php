<?php

namespace app\queue\redis\generate;

use app\service\Ai;
use support\Log;
use support\Redis;
use Webman\RedisQueue\Consumer;

class GenerateTestTask implements Consumer
{
    // 要消费的队列名
    public $queue = 'generate_test_task';

    // 连接名，对应 plugin/webman/redis-queue/redis.php 里的连接
    public $connection = 'default';

    // 消费任务
    public function consume($data)
    {
        $taskId = $data['task_id'];
        echo "开始处理生成测试任务: {$taskId}\n";

        try {
            // 设置任务状态为处理中
            $this->setTaskStatus($taskId, 'processing');
            
            // 获取AI配置信息
            $apiModel = $data['model'] !== 'gpt-4o-mini' ? $data['model'] : env('AI_MODEL', 'gpt-4o-mini');
            
            // 构建AI请求数据
            $aiData = [
                'model' => $apiModel,
                'temperature' => 0.8,
                'messages' => [
                    ['role' => 'system', 'content' => $data['prompt_template']],
                    ['role' => 'user', 'content' => $data['user_content']]
                ]
            ];

            $content = '';
            
            // 调用AI服务
            $ai = new Ai();
            $ai->completions($aiData, function ($response) use ($taskId, $data, &$content) {
                var_dump($response);
                // 处理AI响应
                $choices = $response['choices'][0]['message']['content'];
                $content = $choices;
                
                // 解析对话内容
                $dialogues = $this->parseGeneratedDialogue($content);
                
                // 构建结果数据
                $result = [
                    'raw_content' => $content,
                    'dialogues' => $dialogues,
                    'mode' => $data['mode'],
                    'model' => $data['model'],
                    'topic' => $data['topic_text'],
                    'difficulty' => $data['difficulty'],
                    'prompt_module' => $data['prompt_info']['module'],
                    'prompt_info' => $data['prompt_info'],
                    'task_id' => $taskId,
                    'completed_at' => date('Y-m-d H:i:s')
                ];
                
                // 保存结果到Redis
                $this->setTaskResult($taskId, $result);
                
                // 设置任务状态为完成
                $this->setTaskStatus($taskId, 'completed');
                
                echo "任务 {$taskId} 处理完成\n";
            });
            
        } catch (\Exception $e) {
            echo "任务 {$taskId} 处理失败: " . $e->getMessage() . "\n";
            Log::error("GenerateTestTask 失败", [
                'task_id' => $taskId,
                'error' => $e->getMessage(),
                'data' => $data
            ]);
            
            // 设置任务状态为失败
            $this->setTaskStatus($taskId, 'failed');
            $this->setTaskError($taskId, $e->getMessage());
        }
    }

    // 设置任务状态
    private function setTaskStatus($taskId, $status)
    {
        $redis = Redis::connection();
        $key = "task_status:{$taskId}";
        $redis->setex($key, 3600, $status); // 1小时过期
    }
    
    // 设置任务结果
    private function setTaskResult($taskId, $result)
    {
        $redis = Redis::connection();
        $key = "task_result:{$taskId}";
        $redis->setex($key, 3600, json_encode($result)); // 1小时过期
    }
    
    // 设置任务错误信息
    private function setTaskError($taskId, $error)
    {
        $redis = Redis::connection();
        $key = "task_error:{$taskId}";
        $redis->setex($key, 3600, $error); // 1小时过期
    }

    // 解析生成的对话内容
    private function parseGeneratedDialogue($content)
    {
        $dialogues = [];

        // 先尝试解析为完整的JSON数组
        try {
            $jsonData = json_decode($content, true);
            if (is_array($jsonData)) {
                foreach ($jsonData as $index => $item) {
                    $role = $this->determineRole($item['role'] ?? '', $index);
                    
                    $dialogues[] = [
                        'sort_order' => $index,
                        'role' => $role,
                        'content_zh' => $item['dialogue'] ?? $item['content'] ?? $item['text'] ?? '',
                        'content_vi' => '' // 暂时不翻译
                    ];
                }
                
                if (!empty($dialogues)) {
                    return $dialogues;
                }
            }
        } catch (\Exception $e) {
            // JSON解析失败，继续尝试其他格式
        }

        // 尝试解析单个JSON对象格式
        $regex = '/\{\s*["\'](role|角色)["\']\s*:\s*["\'](.*?)["\']\s*,\s*["\'](dialogue|对话|content|内容)["\']\s*:\s*["\'](.*?)["\']\s*\}/s';
        preg_match_all($regex, $content, $matches, PREG_SET_ORDER);

        if (!empty($matches)) {
            foreach ($matches as $index => $match) {
                $role = $this->determineRole($match[2], $index);
                
                $dialogues[] = [
                    'sort_order' => $index,
                    'role' => $role,
                    'content_zh' => $match[4],
                    'content_vi' => ''
                ];
            }
        }

        // 如果JSON解析失败，尝试其他格式
        if (empty($dialogues)) {
            // 尝试解析类似 "A: 内容" 或 "B: 内容" 的格式
            $lines = explode("\n", $content);
            $filteredLines = array_filter($lines, function($line) {
                return !empty(trim($line));
            });

            foreach ($filteredLines as $index => $line) {
                $line = trim($line);
                if (empty($line)) continue;

                // 检查是否有角色标识
                $roleMatch = null;
                if (preg_match('/^([AB系统用户导游游客服务员客人])[：:]\s*(.+)$/u', $line, $roleMatch)) {
                    $roleIndicator = $roleMatch[1];
                    $content_zh = $roleMatch[2];
                    $role = $this->determineRoleByIndicator($roleIndicator);
                } else {
                    // 没有角色标识，使用简单的交替规则
                    $content_zh = $line;
                    $role = ($index % 2 === 0) ? 'system' : 'user';
                }

                $dialogues[] = [
                    'sort_order' => count($dialogues),
                    'role' => $role,
                    'content_zh' => $content_zh,
                    'content_vi' => ''
                ];
            }
        }

        return $dialogues;
    }

    // 根据角色名称确定角色类型
    private function determineRole($roleStr, $index = 0)
    {
        $roleStr = strtolower($roleStr);
        
        // 系统角色关键词
        $systemKeywords = ['导游', '系统', 'system', '服务员', '老师', '工作人员', '商家', '客服', '接待员'];
        // 用户角色关键词  
        $userKeywords = ['游客', '用户', 'user', '客人', '学生', '顾客', '访客'];
        
        foreach ($systemKeywords as $keyword) {
            if (strpos($roleStr, $keyword) !== false) {
                return 'system';
            }
        }
        
        foreach ($userKeywords as $keyword) {
            if (strpos($roleStr, $keyword) !== false) {
                return 'user';
            }
        }
        
        // 如果无法确定，使用交替规则
        return ($index % 2 === 0) ? 'system' : 'user';
    }
    
    // 根据角色标识符确定角色类型
    private function determineRoleByIndicator($indicator)
    {
        $systemIndicators = ['A', '系统', '导游', '服务员'];
        $userIndicators = ['B', '用户', '游客', '客人'];
        
        if (in_array($indicator, $systemIndicators)) {
            return 'system';
        } elseif (in_array($indicator, $userIndicators)) {
            return 'user';
        }
        
        return 'user'; // 默认为用户
    }

    public function onConsumeFailure(\Throwable $e, $package)
    {
        echo "GenerateTestTask 消费失败: " . $e->getMessage() . "\n";
        Log::error("GenerateTestTask 消费失败", [
            'error' => $e->getMessage(),
            'package' => $package
        ]);
        
        // 尝试从包中获取任务ID并设置失败状态
        if (isset($package['task_id'])) {
            $this->setTaskStatus($package['task_id'], 'failed');
            $this->setTaskError($package['task_id'], $e->getMessage());
        }
    }
}
