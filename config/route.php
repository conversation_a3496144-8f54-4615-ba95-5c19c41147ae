<?php

use app\controller\AiController;
use plugin\admin\app\controller\IndexController;
use Webman\Route;

# ai
Route::group('/ai', function () {
    Route::get('/completions', [AiController::class, 'completions']);

    # AI配置管理（生产环境）
    Route::get('/ai-config', [\app\controller\AiConfigController::class, 'index']);
    Route::get('/ai-config/enhanced', [\app\controller\AiConfigController::class, 'enhanced']);
    Route::get('/ai-config/admin', [\app\controller\AiConfigController::class, 'admin']);
    Route::get('/ai-config/switcher', [\app\controller\AiConfigController::class, 'switcher']);

    # AI配置管理API（生产环境）
    Route::group('/ai-config', function () {
        // 平台管理
        Route::get('/platforms', [\app\controller\AiConfigController::class, 'platforms']);
        Route::get('/platform', [\app\controller\AiConfigController::class, 'getPlatform']);
        Route::post('/platforms', [\app\controller\AiConfigController::class, 'createPlatform']);
        Route::put('/platforms', [\app\controller\AiConfigController::class, 'updatePlatform']);
        Route::delete('/platforms', [\app\controller\AiConfigController::class, 'deletePlatform']);

        // 模型管理
        Route::get('/models', [\app\controller\AiConfigController::class, 'models']);
        Route::get('/all-models', [\app\controller\AiConfigController::class, 'getAllModels']);
        Route::get('/model', [\app\controller\AiConfigController::class, 'getModel']);
        Route::post('/models', [\app\controller\AiConfigController::class, 'createModel']);
        Route::put('/models', [\app\controller\AiConfigController::class, 'updateModel']);
        Route::delete('/models', [\app\controller\AiConfigController::class, 'deleteModel']);

        // 配置管理
        Route::get('/configurations', [\app\controller\AiConfigController::class, 'configurations']);
        Route::get('/config', [\app\controller\AiConfigController::class, 'getConfig']);
        Route::post('/configurations', [\app\controller\AiConfigController::class, 'createConfig']);
        Route::put('/configurations', [\app\controller\AiConfigController::class, 'updateConfig']);
        Route::delete('/configurations', [\app\controller\AiConfigController::class, 'deleteConfig']);
        Route::post('/set-default', [\app\controller\AiConfigController::class, 'setDefault']);
        Route::post('/test-config', [\app\controller\AiConfigController::class, 'testConfig']);
        Route::post('/advanced-test', [\app\controller\AiConfigController::class, 'advancedTestConfig']);
        Route::post('/copy-config', [\app\controller\AiConfigController::class, 'copyConfig']);
        Route::post('/batch-operation', [\app\controller\AiConfigController::class, 'batchOperation']);

        // 模型切换优化功能
        Route::get('/active-configurations', [\app\controller\AiConfigController::class, 'getActiveConfigurations']);
        Route::get('/current-config', [\app\controller\AiConfigController::class, 'getCurrentConfig']);
        Route::post('/switch-config', [\app\controller\AiConfigController::class, 'switchConfig']);
        Route::get('/config-stats', [\app\controller\AiConfigController::class, 'getConfigStats']);
    });
});

# AI配置管理
Route::get('/ai-config', [\app\controller\AiConfigController::class, 'index']);
Route::get('/ai-config/enhanced', [\app\controller\AiConfigController::class, 'enhanced']);
Route::get('/ai-config/admin', [\app\controller\AiConfigController::class, 'admin']);
Route::get('/ai-config/switcher', [\app\controller\AiConfigController::class, 'switcher']);

Route::group('/ai-config', function () {
    // 平台管理
    Route::get('/platforms', [\app\controller\AiConfigController::class, 'platforms']);
    Route::get('/platform', [\app\controller\AiConfigController::class, 'getPlatform']);
    Route::post('/platforms', [\app\controller\AiConfigController::class, 'createPlatform']);
    Route::put('/platforms', [\app\controller\AiConfigController::class, 'updatePlatform']);
    Route::delete('/platforms', [\app\controller\AiConfigController::class, 'deletePlatform']);

    // 模型管理
    Route::get('/models', [\app\controller\AiConfigController::class, 'models']);
    Route::get('/all-models', [\app\controller\AiConfigController::class, 'getAllModels']);
    Route::get('/model', [\app\controller\AiConfigController::class, 'getModel']);
    Route::post('/models', [\app\controller\AiConfigController::class, 'createModel']);
    Route::put('/models', [\app\controller\AiConfigController::class, 'updateModel']);
    Route::delete('/models', [\app\controller\AiConfigController::class, 'deleteModel']);

    // 配置管理
    Route::get('/configurations', [\app\controller\AiConfigController::class, 'configurations']);
    Route::get('/config', [\app\controller\AiConfigController::class, 'getConfig']);
    Route::post('/configurations', [\app\controller\AiConfigController::class, 'createConfig']);
    Route::put('/configurations', [\app\controller\AiConfigController::class, 'updateConfig']);
    Route::delete('/configurations', [\app\controller\AiConfigController::class, 'deleteConfig']);
    Route::post('/set-default', [\app\controller\AiConfigController::class, 'setDefault']);
    Route::post('/test-config', [\app\controller\AiConfigController::class, 'testConfig']);
    Route::post('/advanced-test', [\app\controller\AiConfigController::class, 'advancedTestConfig']);
    Route::post('/copy-config', [\app\controller\AiConfigController::class, 'copyConfig']);
    Route::post('/batch-operation', [\app\controller\AiConfigController::class, 'batchOperation']);

    // 模型切换优化功能
    Route::get('/active-configurations', [\app\controller\AiConfigController::class, 'getActiveConfigurations']);
    Route::get('/current-config', [\app\controller\AiConfigController::class, 'getCurrentConfig']);
    Route::post('/switch-config', [\app\controller\AiConfigController::class, 'switchConfig']);
    Route::get('/config-stats', [\app\controller\AiConfigController::class, 'getConfigStats']);
});

Route::any('/kkdsgjhnxmcng', [IndexController::class, 'index']);

# 测试环境路由（无前缀）
# 首页（测试版本）
Route::get('/', [\app\controller\IndexController::class, 'home']);

# 对话质量检查相关路由（测试版本）
Route::get('/quality', [\app\controller\IndexController::class, 'quality']);
Route::get('/topics', [\app\controller\IndexController::class, 'topics']);
Route::get('/random', [\app\controller\IndexController::class, 'random']);

# 对话生成测试相关路由（测试版本）
Route::get('/generator', [\app\controller\IndexController::class, 'generator']);
Route::post('/generateTest', [\app\controller\IndexController::class, 'generateTest']);
Route::get('/getPrompts', [\app\controller\IndexController::class, 'getPrompts']);
Route::get('/getTaskStatus', [\app\controller\IndexController::class, 'getTaskStatus']);

# 生产环境路由（带/ai前缀）
Route::group('/ai', function () {
    # 对话质量检查相关路由（生产版本）
    Route::get('/quality', [\app\controller\IndexController::class, 'quality']);
    Route::get('/topics', [\app\controller\IndexController::class, 'topics']);
    Route::get('/random', [\app\controller\IndexController::class, 'random']);

    # 对话生成测试相关路由（生产版本）
    Route::get('/generator', [\app\controller\IndexController::class, 'generator']);
    Route::post('/generateTest', [\app\controller\IndexController::class, 'generateTest']);
    Route::get('/getPrompts', [\app\controller\IndexController::class, 'getPrompts']);
    Route::get('/getTaskStatus', [\app\controller\IndexController::class, 'getTaskStatus']);
});
